package com.sdesrd.filetransfer.demo;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.FileInfo;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输客户端演示应用
 * 
 * 这个应用展示了文件传输客户端SDK的完整功能，包括：
 * 1. 文件上传演示
 * 2. 文件下载演示
 * 3. 文件删除演示
 * 4. 错误处理演示
 * 5. 性能测试演示
 * 6. 断点续传演示
 * 7. 并发传输演示
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-19
 */
@Slf4j
public class FileTransferClientDemo {

    /** 演示应用版本 */
    private static final String DEMO_VERSION = "1.0.0";
    
    /** 演示文件目录常量 */
    private static final String UPLOAD_DIR = getSystemProperty("demo.upload.dir", "demo-files/upload");
    private static final String DOWNLOAD_DIR = getSystemProperty("demo.download.dir", "demo-files/download");
    
    /** 服务器连接配置常量 */
    private static final String SERVER_HOST = getSystemProperty("demo.server.host", "localhost");
    private static final int SERVER_PORT = getIntSystemProperty("demo.server.port", 49011);
    private static final String USER_NAME = getSystemProperty("demo.user.name", "demo");
    private static final String USER_SECRET = getSystemProperty("demo.user.secret", "demo-secret-key-2024");
    
    /** 传输配置常量 */
    private static final int CHUNK_SIZE = getIntSystemProperty("demo.chunk.size", 1024 * 1024); // 1MB
    private static final int MAX_CONCURRENT_TRANSFERS = getIntSystemProperty("demo.max.concurrent.transfers", 3);
    private static final int RETRY_COUNT = getIntSystemProperty("demo.retry.count", 3);
    private static final int RETRY_DELAY = getIntSystemProperty("demo.retry.delay", 1000);
    
    /** 测试文件大小常量 */
    private static final int SMALL_FILE_SIZE = getIntSystemProperty("demo.small.file.size", 10 * 1024); // 10KB
    private static final int MEDIUM_FILE_SIZE = getIntSystemProperty("demo.medium.file.size", 1024 * 1024); // 1MB
    private static final int LARGE_FILE_SIZE = getIntSystemProperty("demo.large.file.size", 10 * 1024 * 1024); // 10MB
    
    /** 演示用的传输监听器 */
    private static final TransferListener DEMO_LISTENER = new DemoTransferListener();
    
    /** 客户端实例 */
    private static FileTransferClient client;
    
    /** 控制台输入 */
    private static Scanner scanner;
    
    /** 演示结果统计 */
    private static final DemoStatistics statistics = new DemoStatistics();

    /**
     * 主方法 - 演示应用入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        boolean isAutomatedTest = isAutomatedTestMode(args);
        int exitCode = 0;
        
        printWelcomeMessage();
        
        try {
            // 初始化演示环境
            initializeDemoEnvironment();
            
            // 检查演示环境
            if (!checkDemoEnvironment()) {
                log.error("演示环境检查失败");
                if (isAutomatedTest) {
                    System.exit(1);
                }
                return;
            }
            
            // 检查服务器连接
            if (!checkServerConnection()) {
                log.error("无法连接到服务器，请确保服务器已启动");
                if (isAutomatedTest) {
                    System.exit(1);
                }
                return;
            }
            
            // 运行演示流程
            boolean demoSuccess = runDemoSequence();
            
            // 显示演示结果
            printDemoResults();
            
            // 验证演示结果并设置退出码
            if (isAutomatedTest) {
                exitCode = validateDemoResults(demoSuccess);
                if (exitCode == 0) {
                    log.info("✅ 自动化测试通过 - 所有演示功能正常工作");
                    System.out.println("✅ 自动化测试通过 - 所有演示功能正常工作");
                } else {
                    log.error("❌ 自动化测试失败 - 部分演示功能存在问题");
                    System.out.println("❌ 自动化测试失败 - 部分演示功能存在问题");
                }
            }
            
        } catch (Exception e) {
            log.error("演示程序运行失败", e);
            System.out.println("❌ 演示程序执行异常: " + e.getMessage());
            exitCode = 1;
        } finally {
            cleanup();
            
            // 如果是自动化测试模式，设置退出码
            if (isAutomatedTest) {
                System.exit(exitCode);
            }
        }
    }

    /**
     * 打印欢迎信息
     */
    private static void printWelcomeMessage() {
        System.out.println("========================================");
        System.out.println("    文件传输客户端SDK演示应用");
        System.out.println("    版本: " + DEMO_VERSION);
        System.out.println("    时间: " + java.time.LocalDateTime.now());
        System.out.println("========================================");
        System.out.println();
        
        System.out.println("演示配置:");
        System.out.println("  服务器地址: " + SERVER_HOST + ":" + SERVER_PORT);
        System.out.println("  用户名称: " + USER_NAME);
        System.out.println("  分块大小: " + FileUtils.formatFileSize(CHUNK_SIZE));
        System.out.println("  最大并发: " + MAX_CONCURRENT_TRANSFERS);
        System.out.println("  重试次数: " + RETRY_COUNT);
        System.out.println();
    }

    /**
     * 初始化演示环境
     */
    private static void initializeDemoEnvironment() throws IOException {
        log.info("初始化演示环境...");
        
        // 创建演示目录
        createDemoDirectories();
        
        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr(SERVER_HOST)
                .serverPort(SERVER_PORT)
                .auth(USER_NAME, USER_SECRET)
                .chunkSize(CHUNK_SIZE)
                .maxConcurrentTransfers(MAX_CONCURRENT_TRANSFERS)
                .retry(RETRY_COUNT, RETRY_DELAY)
                .build();

        // 设置超时时间
        config.setConnectTimeoutSeconds(30);
        config.setReadTimeoutSeconds(60);
        config.setWriteTimeoutSeconds(60);
        
        // 创建客户端实例
        client = new FileTransferClient(config);
        
        // 初始化控制台输入
        scanner = new Scanner(System.in);
        
        log.info("演示环境初始化完成");
    }

    /**
     * 创建演示目录
     */
    private static void createDemoDirectories() throws IOException {
        Path uploadPath = Paths.get(UPLOAD_DIR);
        Path downloadPath = Paths.get(DOWNLOAD_DIR);
        
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
            log.info("创建上传目录: {}", uploadPath.toAbsolutePath());
        }
        
        if (!Files.exists(downloadPath)) {
            Files.createDirectories(downloadPath);
            log.info("创建下载目录: {}", downloadPath.toAbsolutePath());
        }
    }

    /**
     * 检查服务器连接
     */
    private static boolean checkServerConnection() {
        try {
            log.info("检查服务器连接...");
            
            // 尝试连接服务器（这里可以实现一个简单的健康检查）
            // 暂时返回true，实际实现中可以调用健康检查接口
            
            log.info("服务器连接正常");
            return true;
            
        } catch (Exception e) {
            log.error("服务器连接失败", e);
            return false;
        }
    }

    /**
     * 运行演示序列
     * 
     * @return 演示是否成功完成
     */
    private static boolean runDemoSequence() throws Exception {
        System.out.println("开始运行文件传输演示序列...");
        System.out.println();
        
        boolean allDemosSuccess = true;
        
        try {
            // 1. 文件上传演示
            if (!demonstrateFileUpload()) {
                allDemosSuccess = false;
                log.error("文件上传演示失败");
            }
            
            // 2. 文件下载演示
            if (!demonstrateFileDownload()) {
                allDemosSuccess = false;
                log.error("文件下载演示失败");
            }
            
            // 3. 文件信息查询演示
            if (!demonstrateFileInfoQuery()) {
                allDemosSuccess = false;
                log.error("文件信息查询演示失败");
            }
            
            // 4. 断点续传演示
            if (!demonstrateResumeTransfer()) {
                allDemosSuccess = false;
                log.error("断点续传演示失败");
            }
            
            // 5. 并发传输演示
            if (!demonstrateConcurrentTransfer()) {
                allDemosSuccess = false;
                log.error("并发传输演示失败");
            }
            
            // 6. 错误处理演示
            if (!demonstrateErrorHandling()) {
                allDemosSuccess = false;
                log.error("错误处理演示失败");
            }
            
            // 7. 性能测试演示
            if (!demonstratePerformanceTest()) {
                allDemosSuccess = false;
                log.error("性能测试演示失败");
            }
            
        } catch (Exception e) {
            log.error("演示序列执行过程中发生异常", e);
            allDemosSuccess = false;
            throw e;
        }
        
        System.out.println("演示序列执行完成！");
        return allDemosSuccess;
    }

    /**
     * 文件上传演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstrateFileUpload() throws Exception {
        System.out.println("=== 1. 文件上传演示 ===");
        
        boolean allUploadsSuccessful = true;
        
        try {
            // 创建测试文件
            List<File> testFiles = createTestFiles();
            
            for (File testFile : testFiles) {
                System.out.println("上传文件: " + testFile.getName() + " (" + FileUtils.formatFileSize(testFile.length()) + ")");
                
                long startTime = System.currentTimeMillis();
                
                // 异步上传文件
                CompletableFuture<UploadResult> uploadFuture = client.uploadFile(
                        testFile.getAbsolutePath(), null, DEMO_LISTENER);
                
                UploadResult result = uploadFuture.get(60, TimeUnit.SECONDS);
                
                long duration = System.currentTimeMillis() - startTime;
                
                if (result.isSuccess()) {
                    System.out.println("✅ 上传成功 - 文件ID: " + result.getFileId() + 
                                     ", 耗时: " + duration + "ms" +
                                     (result.isFastUpload() ? " (秒传)" : ""));
                    statistics.recordUploadSuccess(testFile.length(), duration);
                } else {
                    System.out.println("❌ 上传失败: " + result.getErrorMessage());
                    statistics.recordUploadFailure();
                    allUploadsSuccessful = false;
                }
            }
        } catch (Exception e) {
            log.error("文件上传演示发生异常", e);
            allUploadsSuccessful = false;
        }
        
        System.out.println();
        return allUploadsSuccessful;
    }

    /**
     * 文件下载演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstrateFileDownload() throws Exception {
        System.out.println("=== 2. 文件下载演示 ===");
        
        try {
            // 这里需要先有已上传的文件ID，实际实现中可以从上传结果中获取
            // 为了演示，我们先上传一个文件然后下载
            
            File uploadFile = createTestFile("download-test.txt", MEDIUM_FILE_SIZE);
            UploadResult uploadResult = client.uploadFileSync(uploadFile.getAbsolutePath(), null, DEMO_LISTENER);
            
            if (uploadResult.isSuccess()) {
                String downloadPath = DOWNLOAD_DIR + "/downloaded-" + uploadFile.getName();
                
                System.out.println("下载文件ID: " + uploadResult.getFileId());
                
                long startTime = System.currentTimeMillis();
                
                CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                        uploadResult.getFileId(), downloadPath, DEMO_LISTENER);
                
                DownloadResult result = downloadFuture.get(60, TimeUnit.SECONDS);
                
                long duration = System.currentTimeMillis() - startTime;
                
                if (result.isSuccess()) {
                    System.out.println("✅ 下载成功 - 保存路径: " + result.getLocalPath() + 
                                     ", 耗时: " + duration + "ms");
                    statistics.recordDownloadSuccess(result.getFileSize(), duration);
                    System.out.println();
                    return true;
                } else {
                    System.out.println("❌ 下载失败: " + result.getErrorMessage());
                    statistics.recordDownloadFailure();
                }
            } else {
                System.out.println("❌ 上传失败，无法进行下载演示: " + uploadResult.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("文件下载演示发生异常", e);
        }
        
        System.out.println();
        return false;
    }

    /**
     * 文件信息查询演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstrateFileInfoQuery() throws Exception {
        System.out.println("=== 3. 文件信息查询演示 ===");
        
        // 上传一个文件用于查询
        File testFile = createTestFile("info-query-test.txt", SMALL_FILE_SIZE);
        UploadResult uploadResult = client.uploadFileSync(testFile.getAbsolutePath(), null, DEMO_LISTENER);
        
        if (uploadResult.isSuccess()) {
            // 查询文件信息
            FileInfo fileInfo = client.getFileInfo(uploadResult.getFileId());
            
            System.out.println("文件信息:");
            System.out.println("  文件ID: " + fileInfo.getFileId());
            System.out.println("  文件名: " + fileInfo.getFileName());
            System.out.println("  文件大小: " + fileInfo.getFormattedSize());
            System.out.println("  文件类型: " + fileInfo.getFileType());
            System.out.println("  相对路径: " + fileInfo.getRelativePath());
            System.out.println("  上传时间: " + fileInfo.getUploadTime());
            
            statistics.recordInfoQuerySuccess();
            System.out.println();
            return true;
        } else {
            System.out.println("❌ 文件上传失败，无法查询信息");
            statistics.recordInfoQueryFailure();
        }
        
        System.out.println();
        return false;
    }

    /**
     * 获取系统属性值
     */
    private static String getSystemProperty(String key, String defaultValue) {
        return System.getProperty(key, defaultValue);
    }

    /**
     * 获取整数类型的系统属性值
     */
    private static int getIntSystemProperty(String key, int defaultValue) {
        String value = System.getProperty(key);
        if (StringUtils.isNotBlank(value)) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.warn("无效的系统属性值: {} = {}, 使用默认值: {}", key, value, defaultValue);
            }
        }
        return defaultValue;
    }

    /**
     * 创建测试文件列表
     */
    private static List<File> createTestFiles() throws IOException {
        List<File> files = new ArrayList<>();
        
        files.add(createTestFile("small-test.txt", SMALL_FILE_SIZE));
        files.add(createTestFile("medium-test.txt", MEDIUM_FILE_SIZE));
        
        return files;
    }

    /**
     * 创建指定大小的测试文件
     */
    private static File createTestFile(String fileName, int size) throws IOException {
        File file = new File(UPLOAD_DIR, fileName);
        
        if (!file.exists()) {
            try (FileOutputStream fos = new FileOutputStream(file)) {
                // 创建测试内容
                StringBuilder content = new StringBuilder();
                String line = "这是测试文件内容，用于演示文件传输功能。文件名: " + fileName + "\n";
                
                while (content.length() < size) {
                    content.append(line);
                }
                
                // 截取到指定大小
                String finalContent = content.substring(0, Math.min(size, content.length()));
                IOUtils.write(finalContent, fos, StandardCharsets.UTF_8);
            }
        }
        
        return file;
    }

    /**
     * 断点续传演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstrateResumeTransfer() throws Exception {
        System.out.println("=== 4. 断点续传演示 ===");

        // 创建大文件用于断点续传测试
        File largeFile = createTestFile("resume-test.txt", LARGE_FILE_SIZE);
        System.out.println("创建大文件用于断点续传测试: " + largeFile.getName() +
                         " (" + FileUtils.formatFileSize(largeFile.length()) + ")");

        try {
            // 第一次上传（可能会被中断）
            System.out.println("开始第一次上传...");
            UploadResult firstUpload = client.uploadFileSync(largeFile.getAbsolutePath(), null, DEMO_LISTENER);

            if (firstUpload.isSuccess()) {
                System.out.println("✅ 第一次上传成功 - 文件ID: " + firstUpload.getFileId());

                // 下载文件进行断点续传测试
                String downloadPath = DOWNLOAD_DIR + "/resume-download-" + largeFile.getName();

                System.out.println("开始分片下载（模拟断点续传）...");
                DownloadResult downloadResult = client.downloadFileChunkSync(
                        firstUpload.getFileId(), downloadPath, DEMO_LISTENER);

                if (downloadResult.isSuccess()) {
                    System.out.println("✅ 断点续传下载成功");
                    System.out.println("   保存路径: " + downloadResult.getLocalPath());
                    System.out.println("   文件大小: " + FileUtils.formatFileSize(downloadResult.getFileSize()));

                    // 验证文件完整性
                    if (verifyFileIntegrity(largeFile, new File(downloadResult.getLocalPath()))) {
                        System.out.println("✅ 文件完整性验证通过");
                        statistics.recordResumeTransfer();
                        System.out.println();
                        return true;
                    } else {
                        System.out.println("❌ 文件完整性验证失败");
                    }
                } else {
                    System.out.println("❌ 断点续传下载失败: " + downloadResult.getErrorMessage());
                }
            } else {
                System.out.println("❌ 第一次上传失败: " + firstUpload.getErrorMessage());
            }

        } catch (Exception e) {
            System.out.println("❌ 断点续传演示失败: " + e.getMessage());
            log.error("断点续传演示失败", e);
        }

        System.out.println();
        return false;
    }

    /**
     * 并发传输演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstrateConcurrentTransfer() throws Exception {
        System.out.println("=== 5. 并发传输演示 ===");

        // 创建多个测试文件
        List<File> concurrentFiles = new ArrayList<>();
        for (int i = 1; i <= MAX_CONCURRENT_TRANSFERS; i++) {
            File file = createTestFile("concurrent-test-" + i + ".txt", MEDIUM_FILE_SIZE);
            concurrentFiles.add(file);
        }

        System.out.println("创建 " + concurrentFiles.size() + " 个文件用于并发传输测试");

        // 并发上传
        System.out.println("开始并发上传...");
        List<CompletableFuture<UploadResult>> uploadFutures = new ArrayList<>();

        long concurrentStartTime = System.currentTimeMillis();

        for (File file : concurrentFiles) {
            CompletableFuture<UploadResult> future = client.uploadFile(
                    file.getAbsolutePath(), null, new DemoTransferListener(false));
            uploadFutures.add(future);
        }

        // 等待所有上传完成
        List<UploadResult> uploadResults = new ArrayList<>();
        for (CompletableFuture<UploadResult> future : uploadFutures) {
            try {
                UploadResult result = future.get(120, TimeUnit.SECONDS);
                uploadResults.add(result);
            } catch (Exception e) {
                System.out.println("❌ 并发上传异常: " + e.getMessage());
            }
        }

        long concurrentUploadTime = System.currentTimeMillis() - concurrentStartTime;

        // 统计并发上传结果
        int successCount = 0;
        for (UploadResult result : uploadResults) {
            if (result.isSuccess()) {
                successCount++;
            }
        }

        System.out.println("并发上传结果: " + successCount + "/" + concurrentFiles.size() +
                         " 成功, 总耗时: " + concurrentUploadTime + "ms");

        if (successCount > 0) {
            statistics.recordConcurrentTransfer();

            // 计算并发传输的平均性能
            long totalBytes = concurrentFiles.size() * MEDIUM_FILE_SIZE;
            double throughputMBps = (totalBytes / (1024.0 * 1024.0)) / (concurrentUploadTime / 1000.0);
            System.out.println("并发传输吞吐量: " + String.format("%.2f MB/s", throughputMBps));
            System.out.println();
            return true;
        }

        System.out.println();
        return false;
    }

    /**
     * 错误处理演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstrateErrorHandling() throws Exception {
        System.out.println("=== 6. 错误处理演示 ===");

        // 1. 测试无效文件ID下载
        System.out.println("测试无效文件ID下载...");
        try {
            String invalidFileId = "invalid-file-id-12345";
            String downloadPath = DOWNLOAD_DIR + "/invalid-download.txt";

            DownloadResult result = client.downloadFileSync(invalidFileId, downloadPath, DEMO_LISTENER);

            if (!result.isSuccess()) {
                System.out.println("✅ 正确处理无效文件ID错误: " + result.getErrorMessage());
                statistics.recordErrorHandlingTest();
            } else {
                System.out.println("❌ 未正确处理无效文件ID错误");
            }
        } catch (Exception e) {
            System.out.println("✅ 正确抛出异常处理无效文件ID: " + e.getMessage());
            statistics.recordErrorHandlingTest();
        }

        // 2. 测试不存在的文件上传
        System.out.println("测试不存在的文件上传...");
        try {
            String nonExistentFile = UPLOAD_DIR + "/non-existent-file.txt";

            UploadResult result = client.uploadFileSync(nonExistentFile, null, DEMO_LISTENER);

            if (!result.isSuccess()) {
                System.out.println("✅ 正确处理文件不存在错误: " + result.getErrorMessage());
                statistics.recordErrorHandlingTest();
            } else {
                System.out.println("❌ 未正确处理文件不存在错误");
            }
        } catch (Exception e) {
            System.out.println("✅ 正确抛出异常处理文件不存在: " + e.getMessage());
            statistics.recordErrorHandlingTest();
        }

        // 3. 测试无效路径下载
        System.out.println("测试无效路径下载...");
        try {
            // 先上传一个文件
            File testFile = createTestFile("error-test.txt", SMALL_FILE_SIZE);
            UploadResult uploadResult = client.uploadFileSync(testFile.getAbsolutePath(), null, DEMO_LISTENER);

            if (uploadResult.isSuccess()) {
                String invalidDownloadPath = "/invalid/path/download.txt";

                DownloadResult result = client.downloadFileSync(uploadResult.getFileId(),
                        invalidDownloadPath, DEMO_LISTENER);

                if (!result.isSuccess()) {
                    System.out.println("✅ 正确处理无效下载路径错误: " + result.getErrorMessage());
                    statistics.recordErrorHandlingTest();
                } else {
                    System.out.println("❌ 未正确处理无效下载路径错误");
                }
            }
        } catch (Exception e) {
            System.out.println("✅ 正确抛出异常处理无效下载路径: " + e.getMessage());
            statistics.recordErrorHandlingTest();
        }

        System.out.println();
        return true;
    }

    /**
     * 性能测试演示
     * 
     * @return 演示是否成功
     */
    private static boolean demonstratePerformanceTest() throws Exception {
        System.out.println("=== 7. 性能测试演示 ===");

        // 创建大文件进行性能测试
        File performanceTestFile = createTestFile("performance-test.txt", LARGE_FILE_SIZE);
        System.out.println("创建性能测试文件: " + performanceTestFile.getName() +
                         " (" + FileUtils.formatFileSize(performanceTestFile.length()) + ")");

        // 上传性能测试
        System.out.println("开始上传性能测试...");
        long uploadStartTime = System.currentTimeMillis();

        UploadResult uploadResult = client.uploadFileSync(performanceTestFile.getAbsolutePath(),
                null, new DemoTransferListener(false));

        long uploadEndTime = System.currentTimeMillis();
        long uploadDuration = uploadEndTime - uploadStartTime;

        if (uploadResult.isSuccess()) {
            double uploadSizeMB = performanceTestFile.length() / (1024.0 * 1024.0);
            double uploadThroughputMBps = (uploadSizeMB * 1000.0) / uploadDuration;

            System.out.printf("✅ 上传性能测试完成 - 大小: %.2fMB, 耗时: %dms, 吞吐量: %.2f MB/s%n",
                    uploadSizeMB, uploadDuration, uploadThroughputMBps);

            // 下载性能测试
            System.out.println("开始下载性能测试...");
            String downloadPath = DOWNLOAD_DIR + "/performance-download-" + performanceTestFile.getName();

            long downloadStartTime = System.currentTimeMillis();

            DownloadResult downloadResult = client.downloadFileSync(uploadResult.getFileId(),
                    downloadPath, new DemoTransferListener(false));

            long downloadEndTime = System.currentTimeMillis();
            long downloadDuration = downloadEndTime - downloadStartTime;

            if (downloadResult.isSuccess()) {
                double downloadThroughputMBps = (uploadSizeMB * 1000.0) / downloadDuration;

                System.out.printf("✅ 下载性能测试完成 - 大小: %.2fMB, 耗时: %dms, 吞吐量: %.2f MB/s%n",
                        uploadSizeMB, downloadDuration, downloadThroughputMBps);

                statistics.recordPerformanceTest();

                // 验证文件完整性
                if (verifyFileIntegrity(performanceTestFile, new File(downloadResult.getLocalPath()))) {
                    System.out.println("✅ 性能测试文件完整性验证通过");
                    System.out.println();
                    return true;
                } else {
                    System.out.println("❌ 性能测试文件完整性验证失败");
                }
            } else {
                System.out.println("❌ 下载性能测试失败: " + downloadResult.getErrorMessage());
            }
        } else {
            System.out.println("❌ 上传性能测试失败: " + uploadResult.getErrorMessage());
        }

        System.out.println();
        return false;
    }

    /**
     * 打印演示结果
     */
    private static void printDemoResults() {
        System.out.println("========================================");
        System.out.println("           演示结果统计");
        System.out.println("========================================");
        statistics.printStatistics();
        System.out.println("========================================");
    }

    /**
     * 验证文件完整性
     *
     * @param originalFile 原始文件
     * @param downloadedFile 下载的文件
     * @return 是否完整性验证通过
     */
    private static boolean verifyFileIntegrity(File originalFile, File downloadedFile) {
        try {
            if (!downloadedFile.exists()) {
                log.warn("下载文件不存在: {}", downloadedFile.getAbsolutePath());
                return false;
            }

            if (originalFile.length() != downloadedFile.length()) {
                log.warn("文件大小不匹配 - 原始: {}, 下载: {}",
                        originalFile.length(), downloadedFile.length());
                return false;
            }

            // 简单的字节比较（对于大文件可以使用MD5或SHA256）
            byte[] originalBytes = Files.readAllBytes(originalFile.toPath());
            byte[] downloadedBytes = Files.readAllBytes(downloadedFile.toPath());

            if (originalBytes.length != downloadedBytes.length) {
                return false;
            }

            for (int i = 0; i < originalBytes.length; i++) {
                if (originalBytes[i] != downloadedBytes[i]) {
                    log.warn("文件内容不匹配，位置: {}", i);
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("文件完整性验证失败", e);
            return false;
        }
    }

    /**
     * 等待用户输入（用于交互式演示）
     *
     * @param prompt 提示信息
     */
    private static void waitForUserInput(String prompt) {
        if (scanner != null) {
            System.out.print(prompt);
            scanner.nextLine();
        }
    }

    /**
     * 格式化传输速度
     *
     * @param bytesPerSecond 每秒字节数
     * @return 格式化的速度字符串
     */
    private static String formatTransferSpeed(long bytesPerSecond) {
        return FileUtils.formatFileSize(bytesPerSecond) + "/s";
    }

    /**
     * 计算传输吞吐量
     *
     * @param totalBytes 总字节数
     * @param durationMs 传输时间（毫秒）
     * @return 吞吐量（MB/s）
     */
    private static double calculateThroughputMBps(long totalBytes, long durationMs) {
        if (durationMs <= 0) {
            return 0.0;
        }

        double sizeMB = totalBytes / (1024.0 * 1024.0);
        double durationSeconds = durationMs / 1000.0;

        return sizeMB / durationSeconds;
    }

    /**
     * 检查演示环境
     *
     * @return 环境检查是否通过
     */
    private static boolean checkDemoEnvironment() {
        try {
            // 检查上传目录
            Path uploadPath = Paths.get(UPLOAD_DIR);
            if (!Files.exists(uploadPath) || !Files.isDirectory(uploadPath)) {
                log.error("上传目录不存在或不是目录: {}", uploadPath);
                return false;
            }

            // 检查下载目录
            Path downloadPath = Paths.get(DOWNLOAD_DIR);
            if (!Files.exists(downloadPath) || !Files.isDirectory(downloadPath)) {
                log.error("下载目录不存在或不是目录: {}", downloadPath);
                return false;
            }

            // 检查目录权限
            if (!Files.isWritable(uploadPath)) {
                log.error("上传目录不可写: {}", uploadPath);
                return false;
            }

            if (!Files.isWritable(downloadPath)) {
                log.error("下载目录不可写: {}", downloadPath);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("演示环境检查失败", e);
            return false;
        }
    }

    /**
     * 清理演示文件
     */
    private static void cleanupDemoFiles() {
        try {
            // 清理上传目录中的测试文件
            Path uploadPath = Paths.get(UPLOAD_DIR);
            if (Files.exists(uploadPath)) {
                Files.list(uploadPath)
                     .filter(path -> path.getFileName().toString().contains("test"))
                     .forEach(path -> {
                         try {
                             Files.deleteIfExists(path);
                             log.debug("删除测试文件: {}", path);
                         } catch (IOException e) {
                             log.warn("删除测试文件失败: {}", path, e);
                         }
                     });
            }

            // 清理下载目录中的测试文件
            Path downloadPath = Paths.get(DOWNLOAD_DIR);
            if (Files.exists(downloadPath)) {
                Files.list(downloadPath)
                     .filter(path -> path.getFileName().toString().contains("test") ||
                                   path.getFileName().toString().contains("download"))
                     .forEach(path -> {
                         try {
                             Files.deleteIfExists(path);
                             log.debug("删除下载文件: {}", path);
                         } catch (IOException e) {
                             log.warn("删除下载文件失败: {}", path, e);
                         }
                     });
            }

        } catch (Exception e) {
            log.warn("清理演示文件失败", e);
        }
    }

    /**
     * 检查是否为自动化测试模式
     * 
     * @param args 命令行参数
     * @return 是否为自动化测试模式
     */
    private static boolean isAutomatedTestMode(String[] args) {
        // 检查命令行参数
        for (String arg : args) {
            if ("--test".equals(arg) || "--automated".equals(arg)) {
                return true;
            }
        }
        
        // 检查系统属性
        return "true".equals(System.getProperty("demo.automated.test", "false"));
    }
    
    /**
     * 验证演示结果
     * 
     * @param demoSuccess 演示是否成功
     * @return 退出码：0表示成功，1表示失败
     */
    private static int validateDemoResults(boolean demoSuccess) {
        // 基本演示成功性检查
        if (!demoSuccess) {
            return 1;
        }
        
        // 检查统计数据是否显示足够的成功操作
        if (statistics.getUploadSuccessCount() == 0) {
            log.error("验证失败：没有成功的上传操作");
            return 1;
        }
        
        if (statistics.getDownloadSuccessCount() == 0) {
            log.error("验证失败：没有成功的下载操作");
            return 1;
        }
        
        // 计算总操作数和失败数
        int totalOperations = statistics.getUploadSuccessCount() + statistics.getUploadFailureCount() + 
                             statistics.getDownloadSuccessCount() + statistics.getDownloadFailureCount();
        int totalFailures = statistics.getUploadFailureCount() + statistics.getDownloadFailureCount();
        
        if (totalOperations > 0) {
            double failureRate = (double) totalFailures / totalOperations;
            if (failureRate > 0.2) { // 失败率不应超过20%
                log.error("验证失败：失败率过高 {}/{} = {:.2%}", 
                         totalFailures, totalOperations, failureRate);
                return 1;
            }
        }
        
        log.info("演示结果验证通过：成功操作数量充足，失败率在可接受范围内");
        return 0;
    }

    /**
     * 清理资源
     */
    private static void cleanup() {
        // 清理演示文件
        cleanupDemoFiles();

        if (client != null) {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("关闭客户端时发生异常", e);
            }
        }

        if (scanner != null) {
            scanner.close();
        }

        log.info("资源清理完成");
    }
}

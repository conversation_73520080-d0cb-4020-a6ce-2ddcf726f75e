# 文件传输SDK构建和测试指南

本文档详细说明如何使用统一的 `build-and-test.sh` 脚本来编译、测试和部署文件传输SDK项目。

## 概述

文件传输SDK项目提供了一个功能完整的自动化构建和测试脚本 `build-and-test.sh`，它整合了所有构建和测试功能：

- **🏗️ 完整构建流程** - 从环境检查到项目编译的全流程自动化
- **🧪 多层次测试** - 单元测试、集成测试、性能测试的统一管理
- **📊 质量保证** - JaCoCo覆盖率报告和详细的执行报告
- **⚙️ 智能配置** - 自动Java环境检测和Maven配置优化
- **📝 详细日志** - 完整的执行过程记录和错误诊断

## 脚本特性

### 🔧 核心功能
- **Java环境自动配置** - 自动查找和配置Java 8环境
- **Maven集成** - 完整的Maven生命周期管理和并行编译
- **智能清理** - 自动清理构建缓存和测试数据
- **超时控制** - 防止构建和测试过程无限等待
- **错误处理** - 完善的错误检测、报告和恢复机制

### 📊 测试能力
- **单元测试** - 各个组件的独立功能验证
- **集成测试** - 端到端的客户端-服务端交互测试
- **性能测试** - 系统性能指标验证和基准测试
- **覆盖率分析** - JaCoCo代码覆盖率统计和报告生成
- **测试服务器管理** - 自动启停测试服务器环境

### 🚀 自动化优势
- **环境验证** - 自动检查Java、Maven等必需环境
- **依赖管理** - 自动处理模块间依赖和构建顺序
- **产物收集** - 自动收集JAR文件、文档和报告
- **状态监控** - 实时监控构建进度和测试状态
- **结果汇总** - 生成详细的最终执行报告

## 快速开始

### 1. 环境要求

- **JDK**: 1.8 或更高版本（推荐Java 8）
- **Maven**: 3.6.3 或更高版本
- **操作系统**: Windows/Linux/macOS
- **磁盘空间**: 至少2GB可用空间

### 2. 基本使用

```bash
# 完整构建和测试流程（推荐）
./build-and-test.sh

# 仅构建项目（跳过测试）
./build-and-test.sh --build-only

# 查看帮助信息
./build-and-test.sh --help
```

### 3. 查看结果

执行完成后，查看生成的文件：

```bash
# 查看执行日志
ls -la logs/build-and-test-*.log

# 查看最终报告
cat logs/final-report-*.txt

# 查看覆盖率报告
open logs/coverage/*/index.html
```

## 详细使用指南

### 命令行选项

#### 执行模式控制

```bash
# 仅构建模式 - 编译和安装项目，不运行测试
./build-and-test.sh --build-only

# 完整模式 - 构建和测试（默认）
./build-and-test.sh --build-and-test
```

#### Java环境配置

```bash
# 指定Java JDK路径
./build-and-test.sh --java-home /path/to/java8

# 使用系统默认Java（忽略脚本中的默认Java 8路径）
./build-and-test.sh --use-default-java
```

#### 测试控制选项

```bash
# 仅运行单元测试
./build-and-test.sh --unit-only

# 仅运行集成测试
./build-and-test.sh --integration-only

# 包含性能测试
./build-and-test.sh --performance

# 跳过覆盖率报告生成
./build-and-test.sh --skip-coverage
```

#### 执行控制选项

```bash
# 执行前不清理环境
./build-and-test.sh --no-cleanup

# 不生成最终报告
./build-and-test.sh --no-report

# 显示详细输出
./build-and-test.sh --verbose
```

### 使用场景示例

#### 日常开发场景

```bash
# 快速验证代码修改
./build-and-test.sh --build-only

# 运行相关单元测试
./build-and-test.sh --unit-only

# 完整验证（开发完成后）
./build-and-test.sh
```

#### 持续集成场景

```bash
# CI环境完整验证
./build-and-test.sh --performance --verbose

# 快速CI反馈
./build-and-test.sh --unit-only --no-report
```

#### 发布前验证

```bash
# 完整的质量检查
./build-and-test.sh --performance

# 指定生产环境的Java版本
./build-and-test.sh --java-home /opt/java8 --performance
```

## 脚本执行流程

### 阶段1: 环境准备（步骤1-4）

1. **Java环境设置** - 自动检测和配置Java 8环境
2. **Maven环境检查** - 验证Maven版本和配置
3. **项目结构验证** - 检查模块目录和pom.xml文件
4. **环境清理** - 清理之前的构建产物和测试数据

### 阶段2: 项目构建（步骤5-7）

5. **项目编译** - 并行编译所有模块
6. **安装到本地仓库** - 将构建产物安装到本地Maven仓库
7. **构建结果验证** - 验证编译产物的完整性

### 阶段3: 测试执行（步骤8-11）

8. **单元测试** - 运行各模块的单元测试
9. **集成测试** - 启动测试服务器并运行端到端测试
10. **性能测试** - 执行性能基准测试（可选）
11. **覆盖率报告** - 生成JaCoCo代码覆盖率报告

### 阶段4: 结果汇总（步骤12）

12. **最终报告** - 生成详细的执行报告和结果汇总

## 配置和自定义

### 环境变量配置

```bash
# 自定义Java路径
export JAVA_HOME="/opt/java8"

# Maven执行选项
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"
```

### 脚本内部配置

以下常量可在脚本中修改：

```bash
# 超时时间配置
BUILD_TIMEOUT=600     # 构建超时（秒）
TEST_TIMEOUT=1200     # 测试超时（秒）
SERVER_STARTUP_TIMEOUT=30  # 服务器启动超时（秒）

# 端口配置
TEST_SERVER_PORT=49011  # 测试服务器端口

# 目录配置
LOG_DIR="./logs"      # 日志目录
```

### 默认Java路径

脚本会按以下优先级查找Java：

1. 命令行指定的 `--java-home` 路径
2. 环境变量 `JAVA_HOME`
3. 默认路径 `~/.jdks/corretto-1.8.0_452/`
4. 系统PATH中的java命令

## 输出和报告

### 日志文件

执行过程中会生成以下日志文件：

```
logs/
├── build-and-test-YYYYMMDD_HHMMSS.log  # 主执行日志
├── final-report-YYYYMMDD_HHMMSS.txt    # 最终报告
├── coverage/                           # 覆盖率报告目录
│   ├── file-transfer-server-sdk/
│   ├── file-transfer-client-sdk/
│   └── file-transfer-demo/
└── test-server.pid                     # 测试服务器进程ID
```

### 最终报告内容

最终报告包含以下信息：

- **执行概要** - 执行时间、模式、环境信息
- **构建结果** - 各模块编译状态和产物统计
- **测试结果** - 测试通过率、失败详情、覆盖率
- **性能指标** - 执行时间、资源使用情况
- **文件路径** - 日志文件、报告文件位置

### 覆盖率报告

JaCoCo覆盖率报告提供：

- **行覆盖率** - 代码行执行覆盖统计
- **分支覆盖率** - 条件分支覆盖分析
- **方法覆盖率** - 方法级别覆盖统计
- **类覆盖率** - 类级别覆盖分析
- **可视化报告** - HTML格式的详细报告

## 故障排除

### 常见问题

#### 1. Java环境问题

**问题**: Java版本不兼容或JAVA_HOME未设置

```bash
# 解决方案1: 指定Java路径
./build-and-test.sh --java-home /path/to/java8

# 解决方案2: 设置环境变量
export JAVA_HOME="/path/to/java8"
./build-and-test.sh
```

**问题**: 找不到默认Java路径

```bash
# 解决方案: 使用系统默认Java
./build-and-test.sh --use-default-java
```

#### 2. 构建问题

**问题**: 编译超时或失败

```bash
# 解决方案1: 启用详细输出查看错误
./build-and-test.sh --verbose

# 解决方案2: 清理Maven缓存
rm -rf ~/.m2/repository
./build-and-test.sh
```

**问题**: 模块依赖问题

```bash
# 解决方案: 强制清理后重新构建
./build-and-test.sh --no-cleanup  # 查看是否清理导致问题
```

#### 3. 测试问题

**问题**: 测试服务器启动失败

```bash
# 解决方案1: 检查端口占用
lsof -ti:49011 | xargs kill -9

# 解决方案2: 仅运行单元测试
./build-and-test.sh --unit-only
```

**问题**: 集成测试失败

```bash
# 解决方案: 查看详细测试日志
./build-and-test.sh --integration-only --verbose
```

#### 4. 权限问题

**问题**: 脚本无执行权限

```bash
# 解决方案
chmod +x build-and-test.sh
```

**问题**: 日志目录无写权限

```bash
# 解决方案
mkdir -p logs
chmod 755 logs
```

### 调试技巧

#### 1. 启用详细输出

```bash
# 查看完整的执行过程
./build-and-test.sh --verbose
```

#### 2. 分阶段执行

```bash
# 仅验证环境和构建
./build-and-test.sh --build-only

# 然后单独测试
./build-and-test.sh --unit-only
./build-and-test.sh --integration-only
```

#### 3. 查看实时日志

```bash
# 在另一个终端窗口中
tail -f logs/build-and-test-*.log
```

#### 4. 手动验证环境

```bash
# 验证Java环境
java -version
javac -version

# 验证Maven环境
mvn -version

# 验证项目结构
ls -la */pom.xml
```

## 性能优化

### 构建优化

```bash
# 使用并行编译（脚本默认启用）
mvn compile -T 1C

# 跳过不必要的测试
./build-and-test.sh --build-only
```

### 测试优化

```bash
# 仅运行必要的测试
./build-and-test.sh --unit-only

# 跳过耗时的覆盖率报告
./build-and-test.sh --skip-coverage
```

### 系统资源优化

```bash
# 增加Maven内存
export MAVEN_OPTS="-Xmx4g -XX:MaxPermSize=1g"
./build-and-test.sh
```

## 最佳实践

### 开发阶段

```bash
# 代码修改后快速验证
./build-and-test.sh --build-only

# 运行相关测试
./build-and-test.sh --unit-only
```

### 提交前验证

```bash
# 完整验证
./build-and-test.sh

# 检查报告
cat logs/final-report-*.txt
```

### 持续集成

```bash
# CI环境完整验证
./build-and-test.sh --performance --verbose

# 保存构建产物
cp -r logs/coverage/ $CI_ARTIFACTS_DIR/
cp logs/final-report-*.txt $CI_ARTIFACTS_DIR/
```

### 发布准备

```bash
# 使用生产环境Java版本
./build-and-test.sh --java-home /opt/production-java8 --performance

# 验证所有模块
ls -la */target/*.jar
```

## 脚本维护

### 版本信息

- **脚本版本**: 2.0.0
- **支持的Java版本**: 8+
- **支持的Maven版本**: 3.6.3+
- **最后更新**: 基于实际的项目脚本

### 自定义扩展

如需自定义脚本行为，可以修改以下部分：

1. **常量定义** - 修改超时时间、端口、路径等配置
2. **模块列表** - 添加或移除项目模块
3. **测试逻辑** - 自定义测试执行顺序和条件
4. **报告格式** - 调整报告内容和格式

### 联系支持

如遇到脚本相关问题：

1. 查看 `--help` 选项的详细说明
2. 使用 `--verbose` 选项获取详细执行信息
3. 检查日志文件中的错误信息
4. 参考故障排除章节的解决方案
